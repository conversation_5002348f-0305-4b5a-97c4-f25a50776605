from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from datetime import timedelta
import uvicorn

from database import get_db, engine
from models import Base, User
from schemas import UserCreate, UserLogin, AuthResponse, UserResponse, MessageResponse
from auth import (
    get_password_hash, 
    authenticate_user, 
    create_access_token, 
    get_user_by_email,
    get_current_user
)
from config import settings

# Créer les tables de base de données
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="API d'Authentification",
    description="API FastAPI pour l'authentification avec JWT",
    version="1.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En production, spécifier les domaines autorisés
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_model=MessageResponse)
async def root():
    return {"message": "API d'Authentification FastAPI"}

@app.get("/health", response_model=MessageResponse)
async def health_check():
    return {"message": "API en fonctionnement"}

@app.post("/auth/register", response_model=AuthResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """Enregistrer un nouvel utilisateur"""
    try:
        # Vérifier si l'utilisateur existe déjà
        existing_user = get_user_by_email(db, email=user_data.email)
        if existing_user:
            return AuthResponse(error="Cet email existe déjà")
        
        # Hacher le mot de passe
        hashed_password = get_password_hash(user_data.password)
        
        # Créer un nouvel utilisateur
        db_user = User(
            email=user_data.email,
            name=user_data.name,
            password=hashed_password
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        return AuthResponse(success="Compte créé avec succès !")
        
    except Exception as e:
        db.rollback()
        return AuthResponse(error="Une erreur s'est produite")

@app.post("/auth/login", response_model=AuthResponse)
async def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Connecter un utilisateur et retourner un token d'accès"""
    try:
        # Authentifier l'utilisateur
        user = authenticate_user(db, user_credentials.email, user_credentials.password)
        if not user:
            return AuthResponse(error="Identifiants invalides !")
        
        # Créer le token d'accès
        access_token_expires = timedelta(hours=settings.jwt_expiration_hours)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        
        # Retourner les données utilisateur et le token
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            name=user.name,
            email_verified=user.email_verified,
            image=user.image,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        return AuthResponse(
            success="Connexion réussie",
            user=user_response,
            access_token=access_token,
            token_type="bearer"
        )
        
    except Exception as e:
        return AuthResponse(error="Une erreur s'est produite")

@app.post("/auth/logout", response_model=MessageResponse)
async def logout():
    """Déconnecter l'utilisateur (suppression côté client)"""
    return {"message": "Déconnexion réussie"}

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Obtenir les informations de l'utilisateur actuel"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        name=current_user.name,
        email_verified=current_user.email_verified,
        image=current_user.image,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )

@app.get("/users/profile", response_model=UserResponse)
async def get_user_profile(current_user: User = Depends(get_current_user)):
    """Obtenir le profil de l'utilisateur actuel"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        name=current_user.name,
        email_verified=current_user.email_verified,
        image=current_user.image,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
