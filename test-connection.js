// Script de test pour vérifier la connexion entre Next.js et FastAPI
// Exécuter avec: node test-connection.js

const API_BASE_URL = 'http://localhost:8000';

async function testConnection() {
  console.log('🧪 Test de connexion Next.js ↔ FastAPI');
  console.log('=' .repeat(50));

  try {
    // Test 1: Health check
    console.log('1. Test de santé de l\'API...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ API Health:', healthData.message);

    // Test 2: Test d'enregistrement
    console.log('\n2. Test d\'enregistrement...');
    const registerData = {
      email: '<EMAIL>',
      password: 'motdepasse123',
      name: 'Test NextJS User'
    };

    const registerResponse = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registerData),
    });

    const registerResult = await registerResponse.json();
    if (registerResult.success || registerResult.error?.includes('existe déjà')) {
      console.log('✅ Enregistrement:', registerResult.success || 'Utilisateur existe déjà');
    } else {
      console.log('❌ Enregistrement échoué:', registerResult.error);
    }

    // Test 3: Test de connexion
    console.log('\n3. Test de connexion...');
    const loginData = {
      email: '<EMAIL>',
      password: 'motdepasse123'
    };

    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    const loginResult = await loginResponse.json();
    if (loginResult.access_token) {
      console.log('✅ Connexion réussie');
      console.log('📝 Token reçu:', loginResult.access_token.substring(0, 20) + '...');

      // Test 4: Test de route protégée
      console.log('\n4. Test de route protégée...');
      const meResponse = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${loginResult.access_token}`
        }
      });

      const meData = await meResponse.json();
      console.log('✅ Données utilisateur:', {
        id: meData.id,
        email: meData.email,
        name: meData.name
      });

    } else {
      console.log('❌ Connexion échouée:', loginResult.error);
    }

    console.log('\n' + '=' .repeat(50));
    console.log('🎉 Tests terminés ! La connexion Next.js ↔ FastAPI fonctionne.');
    console.log('\n📋 Prochaines étapes:');
    console.log('1. Démarrer le backend: cd backend-api && ./start.bat');
    console.log('2. Démarrer Next.js: npm run dev');
    console.log('3. Visiter: http://localhost:3000');

  } catch (error) {
    console.error('\n❌ Erreur de connexion:', error.message);
    console.log('\n🔧 Vérifications:');
    console.log('- Le backend FastAPI est-il démarré ? (http://localhost:8000)');
    console.log('- Docker est-il en cours d\'exécution ?');
    console.log('- Les ports 8000 et 5432 sont-ils disponibles ?');
  }
}

testConnection();
