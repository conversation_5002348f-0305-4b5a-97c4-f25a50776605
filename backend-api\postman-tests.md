# Tests Postman pour l'API FastAPI

## Configuration de base
- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`

## 1. Test de santé de l'API

### GET /health
```
URL: http://localhost:8000/health
Method: GET
Headers: Aucun
Body: Aucun

Réponse attendue:
{
    "message": "API en fonctionnement"
}
```

## 2. Enregistrement d'un utilisateur

### POST /auth/register
```
URL: http://localhost:8000/auth/register
Method: POST
Headers: 
  Content-Type: application/json

Body (JSON):
{
    "email": "<EMAIL>",
    "password": "motdepasse123",
    "name": "Utilisateur Test"
}

Réponse attendue:
{
    "success": "Compte créé avec succès !",
    "error": null,
    "user": null,
    "access_token": null,
    "token_type": null
}
```

## 3. Connexion d'un utilisateur

### POST /auth/login
```
URL: http://localhost:8000/auth/login
Method: POST
Headers: 
  Content-Type: application/json

Body (JSON):
{
    "email": "<EMAIL>",
    "password": "motdepasse123"
}

Réponse attendue:
{
    "success": "Connexion réussie",
    "error": null,
    "user": {
        "id": "abc123...",
        "email": "<EMAIL>",
        "name": "Utilisateur Test",
        "email_verified": null,
        "image": null,
        "created_at": "2024-01-01T10:00:00",
        "updated_at": "2024-01-01T10:00:00"
    },
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer"
}
```

## 4. Obtenir les informations de l'utilisateur actuel

### GET /auth/me
```
URL: http://localhost:8000/auth/me
Method: GET
Headers: 
  Authorization: Bearer {access_token_from_login}

Body: Aucun

Réponse attendue:
{
    "id": "abc123...",
    "email": "<EMAIL>",
    "name": "Utilisateur Test",
    "email_verified": null,
    "image": null,
    "created_at": "2024-01-01T10:00:00",
    "updated_at": "2024-01-01T10:00:00"
}
```

## 5. Obtenir le profil utilisateur

### GET /users/profile
```
URL: http://localhost:8000/users/profile
Method: GET
Headers: 
  Authorization: Bearer {access_token_from_login}

Body: Aucun

Réponse attendue: (même que /auth/me)
```

## 6. Déconnexion

### POST /auth/logout
```
URL: http://localhost:8000/auth/logout
Method: POST
Headers: Aucun
Body: Aucun

Réponse attendue:
{
    "message": "Déconnexion réussie"
}
```

## Ordre de test recommandé:
1. **Health Check** - Vérifier que l'API fonctionne
2. **Register** - Créer un compte utilisateur
3. **Login** - Se connecter et récupérer le token
4. **Me/Profile** - Tester les routes protégées avec le token
5. **Logout** - Déconnexion

## Notes importantes:
- Copiez le `access_token` de la réponse de login
- Utilisez ce token dans l'en-tête `Authorization: Bearer {token}` pour les routes protégées
- Le token expire après 24 heures par défaut
