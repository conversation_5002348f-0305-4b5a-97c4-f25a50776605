from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>

from database import get_db
from models import User
from schemas import UserCreate, UserLogin, AuthResponse, UserResponse, Token
from auth import (
    get_password_hash, 
    authenticate_user, 
    create_access_token, 
    get_user_by_email
)
from config import settings

router = APIRouter()

@router.post("/register", response_model=AuthResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_user = get_user_by_email(db, email=user_data.email)
        if existing_user:
            return AuthResponse(error="Email already exists")
        
        # Hash the password
        hashed_password = get_password_hash(user_data.password)
        
        # Create new user
        db_user = User(
            email=user_data.email,
            name=user_data.name,
            password=hashed_password
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        return AuthResponse(success="Account created!")
        
    except Exception as e:
        db.rollback()
        return AuthResponse(error="Something went wrong")

@router.post("/login", response_model=AuthResponse)
async def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Login user and return access token"""
    try:
        # Authenticate user
        user = authenticate_user(db, user_credentials.email, user_credentials.password)
        if not user:
            return AuthResponse(error="Invalid credentials!")
        
        # Create access token
        access_token_expires = timedelta(hours=settings.jwt_expiration_hours)
        access_token = create_access_token(
            data={"sub": user.email}, expires_delta=access_token_expires
        )
        
        # Return user data and token
        user_response = UserResponse(
            id=user.id,
            email=user.email,
            name=user.name,
            email_verified=user.email_verified,
            image=user.image,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        return AuthResponse(
            success="Login successful",
            user=user_response,
            access_token=access_token,
            token_type="bearer"
        )
        
    except Exception as e:
        return AuthResponse(error="Something went wrong")

@router.post("/logout")
async def logout():
    """Logout user (client-side token removal)"""
    return {"message": "Logged out successfully"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        name=current_user.name,
        email_verified=current_user.email_verified,
        image=current_user.image,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at
    )
