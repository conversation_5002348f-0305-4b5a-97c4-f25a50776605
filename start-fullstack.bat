@echo off
echo 🚀 Démarrage de l'application complète (Frontend + Backend + Database)
echo.

echo Étape 1: Arrêt des conteneurs existants...
docker-compose -f docker-compose-full.yml down

echo.
echo Étape 2: Construction et démarrage de tous les services...
docker-compose -f docker-compose-full.yml up --build -d

echo.
echo Étape 3: Attente du démarrage des services...
timeout /t 30 /nobreak > nul

echo.
echo Étape 4: Vérification du statut des services...
docker-compose -f docker-compose-full.yml ps

echo.
echo ✅ Application complète démarrée !
echo.
echo 📍 Services disponibles:
echo   - Application complète: http://localhost (via Nginx)
echo   - Frontend Next.js: http://localhost:3000
echo   - Backend FastAPI: http://localhost:8000
echo   - Documentation API: http://localhost:8000/docs
echo   - PostgreSQL: localhost:5432
echo.
echo 🧪 Pour tester:
echo   1. Aller sur http://localhost
echo   2. C<PERSON>er un compte
echo   3. Se connecter
echo   4. Accéder aux pages protégées
echo.
echo Pour arrêter: docker-compose -f docker-compose-full.yml down
echo.
pause
