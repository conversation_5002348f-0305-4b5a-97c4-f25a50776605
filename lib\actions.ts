"use client";

import * as z from "zod";
import { LoginSchema, RegisterSchema } from "@/schemas";
import { authAPI } from "@/lib/api";

// Login action
export const login = async (values: z.infer<typeof LoginSchema>) => {
  const validatedFields = LoginSchema.safeParse(values);
  
  if (!validatedFields.success) {
    return { error: "Invalid form data" };
  }

  const { email, password } = validatedFields.data;

  try {
    const result = await authAPI.login({ email, password });
    
    if (result.error) {
      return { error: result.error };
    }

    if (result.success) {
      return { success: result.success };
    }

    return { success: "Login successful" };
  } catch (error) {
    console.error("Login action error:", error);
    return { error: "Something went wrong" };
  }
};

// Register action
export const register = async (values: z.infer<typeof RegisterSchema>) => {
  const validateFields = RegisterSchema.safeParse(values);
  
  if (!validateFields.success) {
    return { error: "Invalid form data" };
  }

  const { email, password, name } = validateFields.data;

  try {
    const result = await authAPI.register({ email, password, name });
    
    if (result.error) {
      return { error: result.error };
    }

    if (result.success) {
      return { success: result.success };
    }

    return { success: "Account created!" };
  } catch (error) {
    console.error("Register action error:", error);
    return { error: "Something went wrong" };
  }
};

// Logout action
export const logout = async () => {
  try {
    await authAPI.logout();
    return { success: "Logged out successfully" };
  } catch (error) {
    console.error("Logout action error:", error);
    return { error: "Something went wrong" };
  }
};
