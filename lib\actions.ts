"use client";

import * as z from "zod";
import { LoginSchema, RegisterSchema } from "@/schemas";
import { apiService } from "@/lib/api-service";

// Action de connexion
export const login = async (values: z.infer<typeof LoginSchema>) => {
  const validatedFields = LoginSchema.safeParse(values);

  if (!validatedFields.success) {
    return { error: "Données de formulaire invalides" };
  }

  const { email, password } = validatedFields.data;

  try {
    const result = await apiService.login({ email, password });

    if (result.error) {
      return { error: result.error };
    }

    if (result.success) {
      return { success: result.success };
    }

    return { success: "Connexion réussie" };
  } catch (error) {
    console.error("Erreur d'action de connexion:", error);
    return { error: "Une erreur s'est produite" };
  }
};

// Action d'enregistrement
export const register = async (values: z.infer<typeof RegisterSchema>) => {
  const validateFields = RegisterSchema.safeParse(values);

  if (!validateFields.success) {
    return { error: "Données de formulaire invalides" };
  }

  const { email, password, name } = validateFields.data;

  try {
    const result = await apiService.register({ email, password, name });

    if (result.error) {
      return { error: result.error };
    }

    if (result.success) {
      return { success: result.success };
    }

    return { success: "Compte créé avec succès !" };
  } catch (error) {
    console.error("Erreur d'action d'enregistrement:", error);
    return { error: "Une erreur s'est produite" };
  }
};

// Action de déconnexion
export const logout = async () => {
  try {
    await apiService.logout();
    return { success: "Déconnexion réussie" };
  } catch (error) {
    console.error("Erreur d'action de déconnexion:", error);
    return { error: "Une erreur s'est produite" };
  }
};
