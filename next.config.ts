import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Configuration pour Docker
  output: 'standalone',

  // Configuration des images
  images: {
    unoptimized: true,
  },

  // Ignorer ESLint pendant le build en production
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Ignorer TypeScript errors pendant le build en production
  typescript: {
    ignoreBuildErrors: true,
  },

  // Variables d'environnement publiques
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  },
};

export default nextConfig;
