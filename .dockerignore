# Dépendances
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build Next.js
.next/
out/

# Fichiers de production
/build

# Fichiers d'environnement
.env*.local
.env

# Fichiers de vérification
.eslintcache

# Fichiers de test
coverage/

# Fichiers de développement
.vscode/
.idea/

# Fichiers système
.DS_Store
*.pem

# Fichiers de debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Fichiers Vercel
.vercel

# Fichiers TypeScript
*.tsbuildinfo
next-env.d.ts

# Backend
backend-api/

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Git
.git
.gitignore
README.md
