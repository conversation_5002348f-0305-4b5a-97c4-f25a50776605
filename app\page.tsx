import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import{cn} from "@/lib/utils";
import { LoginButton } from "@/components/auth/login-button";
const font = Poppins(
  {
    subsets:["latin"],
    weight:["600"]
  }
)
export default function Home() {
  return (
    <main className="h-screen flex items-center justify-center bg-radial from-sky-400 to-blue-800">
   <div className="space-y-6">
    <h1 className={cn(font.className,"text-6xl font-semibold  text-white dtop-shadow-md")}>Auth</h1>
    <p className="text-lg text-white">simple authenti</p>
    <div>
      <LoginButton>    <Button variant="secondary" size="lg"  >
        sing in
      </Button ></LoginButton>
  
    </div>

   </div>
  </main>
  );
}
