import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { cn } from "@/lib/utils";
import { LoginButton } from "@/components/auth/login-button";
import { Shield, Lock, Users, Zap } from "lucide-react";

const font = Poppins({
  subsets: ["latin"],
  weight: ["600"]
});

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col items-center justify-center min-h-screen text-center">
          {/* Logo et titre principal */}
          <div className="mb-8">
            <div className="flex items-center justify-center mb-6">
              <Shield className="h-16 w-16 text-blue-400 mr-4" />
              <h1 className={cn(
                font.className,
                "text-7xl font-bold drop-shadow-2xl bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
              )}>
                SecureAuth
              </h1>
            </div>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
              Système d'authentification moderne et sécurisé avec FastAPI et Next.js
            </p>
          </div>

          {/* Boutons d'action */}
          <div className="flex flex-col sm:flex-row gap-4 mb-12">
            <LoginButton>
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Lock className="mr-2 h-5 w-5" />
                Se Connecter
              </Button>
            </LoginButton>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-white/20 text-white hover:bg-white/10 px-8 py-3 text-lg font-semibold backdrop-blur-sm"
              onClick={() => window.location.href = '/auth/register'}
            >
              <Users className="mr-2 h-5 w-5" />
              Créer un Compte
            </Button>
          </div>

          {/* Fonctionnalités */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <Shield className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Sécurité Avancée</h3>
              <p className="text-gray-300">Authentification JWT avec chiffrement bcrypt</p>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <Zap className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Performance</h3>
              <p className="text-gray-300">API FastAPI ultra-rapide avec PostgreSQL</p>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <Users className="h-12 w-12 text-pink-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Gestion Utilisateurs</h3>
              <p className="text-gray-300">Interface intuitive pour la gestion des comptes</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-gray-400">
            <p>&copy; 2024 SecureAuth. Système d'authentification moderne.</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
