"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { apiService, User } from '@/lib/api-service';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success?: string; error?: string }>;
  register: (email: string, password: string, name: string) => Promise<{ success?: string; error?: string }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && apiService.isAuthenticated();

  // Charger l'utilisateur au montage du composant
  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      if (apiService.isAuthenticated()) {
        const userData = await apiService.getCurrentUser();
        setUser(userData);
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'utilisateur:', error);
      // Nettoyer le token invalide
      localStorage.removeItem('access_token');
      localStorage.removeItem('token_type');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const result = await apiService.login({ email, password });
      
      if (result.error) {
        return { error: result.error };
      }

      if (result.user) {
        setUser(result.user);
        // Définir un cookie pour le middleware
        document.cookie = `access_token=${result.access_token}; path=/; max-age=${24 * 60 * 60}`;
      }

      return { success: result.success || 'Connexion réussie' };
    } catch (error) {
      console.error('Erreur de connexion:', error);
      return { error: 'Une erreur s\'est produite' };
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      const result = await apiService.register({ email, password, name });
      
      if (result.error) {
        return { error: result.error };
      }

      return { success: result.success || 'Compte créé avec succès !' };
    } catch (error) {
      console.error('Erreur d\'enregistrement:', error);
      return { error: 'Une erreur s\'est produite' };
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
      setUser(null);
      // Nettoyer le cookie
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    } catch (error) {
      console.error('Erreur de déconnexion:', error);
    }
  };

  const refreshUser = async () => {
    if (apiService.isAuthenticated()) {
      const userData = await apiService.getCurrentUser();
      setUser(userData);
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth doit être utilisé dans un AuthProvider');
  }
  return context;
}
