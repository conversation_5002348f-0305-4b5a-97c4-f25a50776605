@echo off
echo 🛠️ Démarrage en mode développement
echo.

echo Étape 1: Démarrage du backend (Docker)...
cd backend-api
start "Backend" cmd /k "docker-compose up --build"
cd ..

echo.
echo Étape 2: Attente du backend...
timeout /t 15 /nobreak > nul

echo.
echo Étape 3: Démarrage du frontend (npm)...
start "Frontend" cmd /k "npm run dev"

echo.
echo ✅ Mode développement démarré !
echo.
echo 📍 Services disponibles:
echo   - Frontend Next.js: http://localhost:3000
echo   - Backend FastAPI: http://localhost:8000
echo   - Documentation API: http://localhost:8000/docs
echo.
echo 📝 Deux fenêtres de terminal ont été ouvertes:
echo   - Une pour le backend (Docker)
echo   - Une pour le frontend (npm)
echo.
echo Pour arrêter: Fermer les fenêtres de terminal
echo.
pause
