"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authAPI, User } from '@/lib/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success?: string; error?: string }>;
  register: (email: string, password: string, name: string) => Promise<{ success?: string; error?: string }>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && authAPI.isAuthenticated();

  // Load user on mount
  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    try {
      if (authAPI.isAuthenticated()) {
        const userData = await authAPI.getCurrentUser();
        setUser(userData);
      }
    } catch (error) {
      console.error('Error loading user:', error);
      // Clear invalid token
      localStorage.removeItem('access_token');
      localStorage.removeItem('token_type');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const result = await authAPI.login({ email, password });
      
      if (result.error) {
        return { error: result.error };
      }

      if (result.user) {
        setUser(result.user);
        // Set cookie for middleware
        document.cookie = `access_token=${result.access_token}; path=/; max-age=${24 * 60 * 60}`;
      }

      return { success: result.success || 'Login successful' };
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Something went wrong' };
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      const result = await authAPI.register({ email, password, name });
      
      if (result.error) {
        return { error: result.error };
      }

      return { success: result.success || 'Account created!' };
    } catch (error) {
      console.error('Register error:', error);
      return { error: 'Something went wrong' };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
      setUser(null);
      // Clear cookie
      document.cookie = 'access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const refreshUser = async () => {
    if (authAPI.isAuthenticated()) {
      const userData = await authAPI.getCurrentUser();
      setUser(userData);
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
