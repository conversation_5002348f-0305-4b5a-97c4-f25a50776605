from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from config import settings

# Créer le moteur SQLAlchemy
engine = create_engine(settings.database_url)

# Créer la classe SessionLocal
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Dépendance pour obtenir une session de base de données
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
