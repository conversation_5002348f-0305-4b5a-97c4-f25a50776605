"use client";

import { useAuth } from "@/lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";

const SettingsPage = () => {
    const { user, logout, isLoading } = useAuth();
    const router = useRouter();

    const handleLogout = async () => {
        await logout();
        router.push("/auth/login");
    };

    if (isLoading) {
        return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
    }

    if (!user) {
        return <div className="flex items-center justify-center min-h-screen">Not authenticated</div>;
    }

    return (
        <div className="container mx-auto p-6 max-w-2xl">
            <h1 className="text-3xl font-bold mb-6">Settings</h1>

            <div className="bg-white shadow-md rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">User Information</h2>
                <div className="space-y-2">
                    <p><strong>ID:</strong> {user.id}</p>
                    <p><strong>Name:</strong> {user.name}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Email Verified:</strong> {user.email_verified ? 'Yes' : 'No'}</p>
                    <p><strong>Created:</strong> {new Date(user.created_at).toLocaleDateString()}</p>
                    <p><strong>Updated:</strong> {new Date(user.updated_at).toLocaleDateString()}</p>
                </div>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Actions</h2>
                <Button
                    onClick={handleLogout}
                    variant="destructive"
                    className="w-full sm:w-auto"
                >
                    Sign Out
                </Button>
            </div>
        </div>
    );
};

export default SettingsPage;
