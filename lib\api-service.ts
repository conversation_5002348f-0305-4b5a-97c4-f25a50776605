// Service API pour communiquer avec le backend FastAPI

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Types TypeScript
export interface User {
  id: string;
  email: string;
  name: string;
  email_verified?: string;
  image?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  success?: string;
  error?: string;
  user?: User;
  access_token?: string;
  token_type?: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

// Fonction helper pour les en-têtes d'authentification
const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Service API
export const apiService = {
  // Test de santé de l'API
  healthCheck: async (): Promise<{ message: string }> => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  },

  // Enregistrement d'un nouvel utilisateur
  register: async (data: RegisterData): Promise<AuthResponse> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Register error:', error);
      return { error: 'Erreur de connexion au serveur' };
    }
  },

  // Connexion utilisateur
  login: async (data: LoginData): Promise<AuthResponse> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // Stocker le token si la connexion réussit
      if (result.access_token) {
        localStorage.setItem('access_token', result.access_token);
        localStorage.setItem('token_type', result.token_type || 'bearer');
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Erreur de connexion au serveur' };
    }
  },

  // Déconnexion utilisateur
  logout: async (): Promise<void> => {
    try {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: getAuthHeaders(),
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Toujours nettoyer le stockage local
      localStorage.removeItem('access_token');
      localStorage.removeItem('token_type');
    }
  },

  // Obtenir les informations de l'utilisateur actuel
  getCurrentUser: async (): Promise<User | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expiré ou invalide
          localStorage.removeItem('access_token');
          localStorage.removeItem('token_type');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  },

  // Obtenir le profil utilisateur
  getUserProfile: async (): Promise<User | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/profile`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Get profile error:', error);
      return null;
    }
  },

  // Vérifier si l'utilisateur est authentifié
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('access_token');
  },
};
