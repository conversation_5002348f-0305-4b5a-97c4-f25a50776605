-- Initialize the database with required extensions and setup
-- This file is automatically executed when the PostgreSQL container starts

-- Create the database if it doesn't exist (handled by POSTGRES_DB env var)
-- Create user if it doesn't exist (handled by POSTGRES_USER env var)

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE auth_db TO auth_user;

-- Create tables will be handled by SQLAlchemy in the FastAPI application
