services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: fullstack_postgres
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend-api/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - fullstack_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  backend:
    build:
      context: ./backend-api
      dockerfile: Dockerfile
    container_name: fullstack_backend
    environment:
      DATABASE_URL: **************************************************/auth_db
      JWT_SECRET_KEY: ma-cle-secrete-jwt-super-securisee-2024
      JWT_ALGORITHM: HS256
      JWT_EXPIRATION_HOURS: 24
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - fullstack_network
    volumes:
      - ./backend-api:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Next.js Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: http://localhost:8000
    container_name: fullstack_frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - fullstack_network

  # Nginx Reverse Proxy (optionnel)
  nginx:
    image: nginx:alpine
    container_name: fullstack_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - fullstack_network

volumes:
  postgres_data:

networks:
  fullstack_network:
    driver: bridge
