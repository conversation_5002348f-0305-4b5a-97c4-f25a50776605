"use client";
import {<PERSON><PERSON>rapper} from "./card-wrapper";
import {useForm} from "react-hook-form";
import {zodR<PERSON>olver} from "@hookform/resolvers/zod";
import {Form,FormControl,FormField,FormItem,FormLabel,FormMessage} from "@/components/ui/form"
import * as z from "zod";
import {RegisterSchema } from "@/schemas";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {FormError} from "@/components/form-error";
import {FormSuccess} from "@/components/form-success";
import { useState } from "react";
import { useAuth } from "@/lib/auth-provider";
import { useRouter } from "next/navigation";
export const RegisterForm = () => {
    const [error, setError] = useState<string | undefined>();
    const [success, setSuccess] = useState<string | undefined>();
    const [isPending, setIsPending] = useState(false);
    const { register } = useAuth();
    const router = useRouter();

    const form = useForm<z.infer<typeof RegisterSchema>>({
        resolver: zodResolver(RegisterSchema),
        defaultValues: {
            email: "",
            password: "",
            name: "",
        },
    });

    const onSubmit = async (values: z.infer<typeof RegisterSchema>) => {
        setError("");
        setSuccess("");
        setIsPending(true);

        try {
            const result = await register(values.email, values.password, values.name);

            if (result.error) {
                setError(result.error);
            } else if (result.success) {
                setSuccess(result.success);
                // Redirect to login after successful registration
                setTimeout(() => {
                    router.push("/auth/login");
                }, 2000);
            }
        } catch (error) {
            setError("Something went wrong");
        } finally {
            setIsPending(false);
        }
    };
  return <CardWrapper
  headerLabel="Créer votre compte"
  backButtonLabel="Déjà un compte ?"
  backButtonHref="/auth/login"
  showSocial>
    <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)
    
    }className="space-y-4" >
        <FormField
        control={form.control}
        name="email"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Email</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="<EMAIL>"
                       type="email"
                       className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-blue-400"
                    >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>
 <FormField
        control={form.control}
        name="name"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Name</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="Votre nom complet"
                       className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-blue-400"
                    >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>
<FormField
        control={form.control}
        name="password"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Password</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="••••••••"
                       type="password"
                       className="bg-white/5 border-white/20 text-white placeholder:text-gray-400 focus:border-blue-400"
                    >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>
<Button
  disabled={isPending}
  type="submit"
  className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
>
  {isPending ? "Création..." : "Créer le Compte"}
</Button>
<FormError message={error}/>
<FormSuccess message={success}/>
        </form>

    </Form>
  </CardWrapper>;
};
