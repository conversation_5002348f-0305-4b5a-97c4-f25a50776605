"use server";
import * as z from "zod";
import {AuthError} from "next-auth";
import {signIn} from "@/auth";
import {LoginSchema } from "@/schemas";
import { DEFAULT_LOGIN_REDIRECT } from "@/routes";
export const login =async(values:z.infer<typeof LoginSchema>)=>{
  const validatedFeilds=LoginSchema.safeParse(values);
  if(!validatedFeilds.success){
    return {error:"Invalid form data"};
  }
  const {email,password}=validatedFeilds.data;
  try{
    await signIn("credentials",{
      email,
      password,
      redirectTo:DEFAULT_LOGIN_REDIRECT,
    });
  }
  catch(error){
    if(error instanceof AuthError){
        switch(error.type){
            case "CredentialsSignin":
                return {error:"Invalid credentials!"};
                default:
                    return {error:"Something went wrong"};
        }
      
    }
    throw error;
    
  }
};