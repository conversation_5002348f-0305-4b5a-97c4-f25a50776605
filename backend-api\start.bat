@echo off
echo Démarrage du backend FastAPI avec PostgreSQL...
echo.

echo Étape 1: Arrêt des conteneurs existants...
docker-compose down

echo.
echo Étape 2: Construction et démarrage des services...
docker-compose up --build -d

echo.
echo Étape 3: Attente du démarrage des services...
timeout /t 15 /nobreak > nul

echo.
echo Étape 4: Vérification du statut des services...
docker-compose ps

echo.
echo ✅ Backend démarré avec succès !
echo.
echo 📍 Services disponibles:
echo   - API FastAPI: http://localhost:8000
echo   - Documentation API: http://localhost:8000/docs
echo   - PostgreSQL: localhost:5432
echo.
echo 🧪 Pour tester avec Postman:
echo   1. GET  http://localhost:8000/health
echo   2. POST http://localhost:8000/auth/register
echo   3. POST http://localhost:8000/auth/login
echo   4. GET  http://localhost:8000/auth/me (avec token)
echo.
echo Pour arrêter: docker-compose down
echo.
pause
