"use server";
import * as z from "zod";
import bcrypt from "bcryptjs";
import {RegisterSchema } from "@/schemas";
import {getUserByEmail} from "@/data/user";
import {db} from "@/lib/db";
export const register =async(values:z.infer<typeof RegisterSchema>)=>{
  const validateFields=RegisterSchema.safeParse(values);
  if(!validateFields.success){
    return {error:"Invalid form data"};
  }
  const{email,password,name}=validateFields.data;
  const hashedPassword=await bcrypt.hash(password,10);
const existingUser=await getUserByEmail(email); 
if(existingUser){
  return {error:"Email already exists"};
}
await db.user.create(
  {
    data: {
      email,
      password:hashedPassword,
      name,
    },
  }
)
// TDO: send verification email
  return {success:"Account created !"};
};