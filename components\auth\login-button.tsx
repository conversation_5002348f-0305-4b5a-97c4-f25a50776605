"use client";
import {useRouter} from "next/navigation";

interface LoginButtonProps {
  children: React.ReactNode;
  mode?:"model"|"redirect";
  asChild?:boolean;

};
export const LoginButton=(
    {
        children,
        mode= "redirect",
        asChild,

    }: LoginButtonProps)=>{
        const router=useRouter();
        const onclick=()=>
        {router.push("/auth/login");

        };
        if (mode=='model')
        {
            return(
                <span>
                    TODO:Implement modal
                </span>
            )
        }
        return(
            <span  onClick={onclick} className="cursor-pointer">
                {children}
            </span>
        )
    }
