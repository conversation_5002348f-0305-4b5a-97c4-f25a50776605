services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: fastapi_postgres
    environment:
      POSTGRES_DB: auth_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - backend_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  fastapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi_backend
    environment:
      DATABASE_URL: **************************************************/auth_db
      JWT_SECRET_KEY: ma-cle-secrete-jwt-super-securisee-2024
      JWT_ALGORITHM: HS256
      JWT_EXPIRATION_HOURS: 24
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - backend_network
    volumes:
      - .:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:

networks:
  backend_network:
    driver: bridge
