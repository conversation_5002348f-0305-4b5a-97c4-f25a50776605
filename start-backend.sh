#!/bin/bash

# Start the backend services with Docker Compose
echo "Starting PostgreSQL and FastAPI backend..."

# Build and start the services
docker-compose up --build -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Check if services are running
echo "Checking service status..."
docker-compose ps

echo "Backend services started!"
echo "FastAPI API: http://localhost:8000"
echo "PostgreSQL: localhost:5432"
echo "API Documentation: http://localhost:8000/docs"
