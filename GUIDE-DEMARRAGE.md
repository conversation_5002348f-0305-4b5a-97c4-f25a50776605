# Guide de Démarrage - Next.js + FastAPI + PostgreSQL

## 🚀 Démarrage Rapide

### 1. <PERSON><PERSON><PERSON><PERSON> le Backend FastAPI
```bash
cd backend-api
./start.bat
```

### 2. Vérifier que le backend fonctionne
```bash
# Tester la connexion
node test-connection.js

# Ou manuellement
curl http://localhost:8000/health
```

### 3. Démarrer le Frontend Next.js
```bash
# Installer les dépendances (si pas encore fait)
npm install

# Démarrer le serveur de développement
npm run dev
```

### 4. Accéder à l'application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Documentation API**: http://localhost:8000/docs

## 🔧 Configuration

### Variables d'environnement

**Backend** (`backend-api/.env`):
```env
DATABASE_URL=**************************************************/auth_db
JWT_SECRET_KEY=ma-cle-secrete-jwt-super-securisee-2024
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
```

**Frontend** (`.env.local`):
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NODE_ENV=development
```

## 📊 Base de Données

### Informations de connexion PostgreSQL:
- **Host**: localhost
- **Port**: 5432
- **Database**: auth_db
- **Username**: auth_user
- **Password**: auth_password

### Se connecter à la base de données:
```bash
# Via Docker
docker exec -it fastapi_postgres psql -U auth_user -d auth_db

# Via client PostgreSQL
psql -h localhost -p 5432 -U auth_user -d auth_db
```

## 🧪 Tests

### Test de l'API avec Postman:
1. **Health Check**: `GET http://localhost:8000/health`
2. **Register**: `POST http://localhost:8000/auth/register`
3. **Login**: `POST http://localhost:8000/auth/login`
4. **Profile**: `GET http://localhost:8000/auth/me` (avec token)

### Test de l'application complète:
1. Aller sur http://localhost:3000
2. Cliquer sur "Sign In"
3. Créer un compte via "Register"
4. Se connecter avec le compte créé
5. Accéder à la page protégée "/settings"

## 🏗️ Architecture

```
Frontend (Next.js)     Backend (FastAPI)     Database (PostgreSQL)
     :3000          ←→        :8000         ←→        :5432
                    
┌─────────────────┐   ┌─────────────────┐   ┌─────────────────┐
│ • Pages         │   │ • API Routes    │   │ • users table   │
│ • Components    │   │ • Auth JWT      │   │ • accounts table│
│ • Auth Context  │   │ • SQLAlchemy    │   │ • Triggers      │
│ • API Service   │   │ • Pydantic      │   │ • Indexes       │
└─────────────────┘   └─────────────────┘   └─────────────────┘
```

## 🔐 Flux d'Authentification

1. **Inscription**: 
   - Frontend → `POST /auth/register` → Backend
   - Mot de passe hashé avec bcrypt
   - Utilisateur créé en base

2. **Connexion**:
   - Frontend → `POST /auth/login` → Backend
   - Vérification mot de passe
   - Génération token JWT
   - Token stocké dans localStorage + cookie

3. **Routes protégées**:
   - Middleware Next.js vérifie le cookie
   - API calls avec token Bearer
   - Backend valide le JWT

## 🛠️ Commandes Utiles

### Backend:
```bash
# Voir les logs
docker-compose logs fastapi

# Redémarrer les services
docker-compose restart

# Arrêter tout
docker-compose down

# Nettoyer et redémarrer
docker-compose down -v && docker-compose up --build -d
```

### Frontend:
```bash
# Développement
npm run dev

# Build production
npm run build

# Linter
npm run lint
```

## 🐛 Dépannage

### Problèmes courants:

1. **Erreur CORS**: Vérifier que `NEXT_PUBLIC_API_URL` est correct
2. **Connexion DB**: Vérifier que PostgreSQL est démarré
3. **Port occupé**: Changer les ports dans docker-compose.yml
4. **Token expiré**: Se reconnecter ou vérifier JWT_EXPIRATION_HOURS

### Logs utiles:
```bash
# Logs backend
docker-compose logs fastapi

# Logs base de données
docker-compose logs postgres

# Status des conteneurs
docker-compose ps
```

## 📝 Prochaines Étapes

1. ✅ Backend FastAPI fonctionnel
2. ✅ Base de données PostgreSQL
3. ✅ Authentification JWT
4. ✅ Frontend Next.js connecté
5. 🔄 Tests et validation
6. 🚀 Déploiement en production
