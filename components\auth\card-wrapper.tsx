"use client";
import {Social} from "./social";
import {BackButton} from "./back-button";
import {<PERSON>,CardContent,CardFooter,CardHeader} from "@/components/ui/card";
interface CardWrapperProps{
    children:React.ReactNode;
    headerLabel:string;
    backButtonLabel:string;
    backButtonHref:string;
    showSocial?:boolean;
};
import {Header } from "./header";
export const CardWrapper = ({
    children,
    headerLabel,
    backButtonLabel,
    backButtonHref,
    showSocial
}:CardWrapperProps) => {
    return (
        <Card className="w-[400px] shadow-md">
            <CardHeader><Header label={headerLabel} /></CardHeader>
         
            
         <CardContent> {children}</CardContent> 
         {
                showSocial &&(
                    <CardFooter>
                        <Social/>
                    </CardFooter>
                )
            } 
            <CardFooter>
                <BackButton href={backButtonHref} label={backButtonLabel}></BackButton>
                </CardFooter>
            
          </Card>);
          }
