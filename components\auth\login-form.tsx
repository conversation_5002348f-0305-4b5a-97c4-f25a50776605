"use client";
import {<PERSON><PERSON>rapper} from "./card-wrapper";
import {useForm} from "react-hook-form";
import {zodResolver} from "@hookform/resolvers/zod";
import {Form,FormControl,FormField,FormItem,FormLabel,FormMessage} from "@/components/ui/form"
import * as z from "zod";
import {LoginSchema } from "@/schemas";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {FormError} from "@/components/form-error";
import {FormSuccess} from "@/components/form-success";
import { useState } from "react";
import { useAuth } from "@/lib/auth-provider";
import { useRouter } from "next/navigation";
export const LoginForm = () => {
    const [error, setError] = useState<string | undefined>();
    const [success, setSuccess] = useState<string | undefined>();
    const [isPending, setIsPending] = useState(false);
    const { login } = useAuth();
    const router = useRouter();

    const form = useForm<z.infer<typeof LoginSchema>>({
        resolver: zodResolver(LoginSchema),
        defaultValues: {
            email: "",
            password: "",
        },
    });

    const onSubmit = async (values: z.infer<typeof LoginSchema>) => {
        setError("");
        setSuccess("");
        setIsPending(true);

        try {
            const result = await login(values.email, values.password);

            if (result.error) {
                setError(result.error);
            } else if (result.success) {
                setSuccess(result.success);
                // Redirect to dashboard after successful login
                setTimeout(() => {
                    router.push("/settings");
                }, 1000);
            }
        } catch (error) {
            setError("Something went wrong");
        } finally {
            setIsPending(false);
        }
    };
  return <CardWrapper
  headerLabel="welcom back"
  backButtonLabel="Don't have an account?"
  backButtonHref="/auth/register"
  showSocial>
    <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)
    
    }className="space-y-4" >
        <FormField
        control={form.control}
        name="email"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Email</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="<EMAIL>"
                       type="email" >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>

<FormField
        control={form.control}
        name="password"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Password</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="********"
                       type="password" >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>
<Button disabled={isPending} type="submit" className="w-full">   Login  </Button>
<FormError message={error}/>
<FormSuccess message={success}/>
        </form>

    </Form>
  </CardWrapper>;
};
