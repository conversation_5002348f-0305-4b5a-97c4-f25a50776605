"use client";
import {<PERSON><PERSON>rapper} from "./card-wrapper";
import {useForm} from "react-hook-form";

import {zodResolver} from "@hookform/resolvers/zod";
import {Form,FormControl,FormField,FormItem,FormLabel,FormMessage} from "@/components/ui/form"
import * as z from "zod";
import {LoginSchema } from "@/schemas";
import {Input} from "@/components/ui/input";
import {Button} from "@/components/ui/button";
import {FormError} from "@/components/form-error";
import {FormSuccess} from "@/components/form-success";
import {login} from "@/actions/login";
import { useState,useTransition } from "react";
export const LoginForm = () => {
    const [error,setError]=useState<string|undefined>();
    const [success,setSuccess]=useState<string|undefined>();
    const [isPending, startTransition] = useTransition();
    const form=useForm<z.infer<typeof LoginSchema>>({
        resolver:zodResolver(LoginSchema),
        defaultValues: {
            email:"",
            password:"",
        },
    });
    const onSubmit=(values: z.infer<typeof LoginSchema>)=>{
        setError("");
        setSuccess("");
        startTransition(()=>{
            login(values)
                  .then((data)=>
            {
                setError(data.error);
                setSuccess(data.success);
            })
        });
       
    };
  return <CardWrapper
  headerLabel="welcom back"
  backButtonLabel="Don't have an account?"
  backButtonHref="/auth/register"
  showSocial>
    <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)
    
    }className="space-y-4" >
        <FormField
        control={form.control}
        name="email"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Email</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="<EMAIL>"
                       type="email" >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>

<FormField
        control={form.control}
        name="password"
        render={({field})=>
            (
                <FormItem>
                   <FormLabel>Password</FormLabel>
                   <FormControl>
                    <Input {...field}
                       disabled={isPending}
                       placeholder="********"
                       type="password" >

                    </Input>
                   </FormControl>
                   <FormMessage/>
                </FormItem>
            )
        }/>
<Button disabled={isPending} type="submit" className="w-full">   Login  </Button>
<FormError message={error}/>
<FormSuccess message={success}/>
        </form>

    </Form>
  </CardWrapper>;
};
